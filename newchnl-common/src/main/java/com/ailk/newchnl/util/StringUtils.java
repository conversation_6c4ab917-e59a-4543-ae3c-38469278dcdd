package com.ailk.newchnl.util;

import org.apache.commons.lang3.ObjectUtils;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 * 
 * <AUTHOR>
 *
 */
public class StringUtils {

    public static String HanDigiStr[] = new String[] {
            "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
    public static String HanDiviStr[] = new String[] {
            "", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿",
            "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿",
            "拾", "佰", "仟", "万", "拾", "佰", "仟"};


	/**
	 * 字符串过滤
	 * 
	 * @param target
	 *            目标字符串
	 * @return 去除特殊字符后的字符串
	 */
	public static String filter(String target) {
		if (target != null) {
			// 待去除的字符
			String regEx = "[`~!@#$%^&*()+=|{}':;',//[//].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
			Pattern p = Pattern.compile(regEx);
			Matcher m = p.matcher(target);
			return m.replaceAll("");
		}
		return null;
	}

	/**
	 * 字符缩略
	 * 
	 * @param target
	 *            目标字符
	 * @param length
	 *            缩略长度
	 * @param append
	 *            添加的后缀
	 * @return 目标缩略
	 */
	public static String substring(String target, int length, String append) {
		if (target == null || "".equals(target.trim()) || target.length() <= length) {
			return target;
		}
		String sub = null;
		if (length <= 0) {
			sub = "";
		} else {
			sub = target.substring(0, length);
		}
		if (append != null) {
			sub += append;
		}
		return sub;
	}

	/**
	 * 去除文本内容里面的HTML标签
	 * 
	 * @param html
	 *            目标内容
	 * @return
	 */
	public static String removeHtmlTag(String html) {

		if (html != null) {
			// 去除含有内容的script
			Pattern scriptPattern = Pattern.compile("<\\s*script[^>]*>.*<\\s*/\\s*script\\s*>");
			html = scriptPattern.matcher(html).replaceAll("");
			// 去除含有内容的style
			Pattern stylePattern = Pattern.compile("<\\s*style[^>]*>.*<\\s*/\\s*style\\s*>");
			html = stylePattern.matcher(html).replaceAll("");
			// 去除HTML标签
			Pattern tagPattern = Pattern.compile("<[^>]+>");
			html = tagPattern.matcher(html).replaceAll("");
			return html;
		}
		return null;
	}

	public static boolean isNullOrBlank(String s) {
		if (s == null) {
			return true;
		}
		if ("".equals(s.trim())) {
			return true;
		}
		return false;
	}

	public static boolean isNotNullOrBlank(String s) {

		if (s == null) {
			return false;
		}

		if ("".equals(s.trim())) {
			return false;
		}

		return true;
	}


    public static String moneyToRMBStr(double val) {
        String SignStr = "";
        String TailStr = "";
        long fraction;
        long integer;
        int jiao;
        int fen;

        if (val < 0) {
            val = -val;
            SignStr = "负";
        }
        if (val > 99999999999999.999 || val < -99999999999999.999) {
            return "数值位数过大!";
        }
        // 四舍五入到分
        long temp = Math.round(val * 100);
        integer = temp / 100;
        fraction = temp % 100;
        jiao = (int) fraction / 10;
        fen = (int) fraction % 10;
        if (jiao == 0 && fen == 0) {
            TailStr = "整";
        } else {
            TailStr = HanDigiStr[jiao];
            if (jiao != 0) {
                TailStr += "角";
            }
            if (integer == 0 && jiao == 0) { // 零元后不写零几分
                TailStr = "";
            }
            if (fen != 0) {
                TailStr += HanDigiStr[fen] + "分";
            }
        }
        // 下一行可用于非正规金融场合，0.03只显示“叁分”而不是“零元叁分”
        //        if( !integer ) return  SignStr+TailStr;
        String tempMoney = SignStr + PositiveIntegerToHanStr(String.valueOf(integer)) +
                "元" + TailStr;
        if (tempMoney.startsWith("拾")
                || tempMoney.startsWith("佰")
                || tempMoney.startsWith("仟")
                || tempMoney.startsWith("万")
                || tempMoney.startsWith("亿")
                ) {
            tempMoney = "壹" + tempMoney;
        }
        tempMoney = "￥" + tempMoney;
        return tempMoney;
    }

    public static String PositiveIntegerToHanStr(String NumStr) { // 输入字符串必须正整数，只允许前导空格(必须右对齐)，不宜有前导零
        String RMBStr = "";
        boolean lastzero = false;
        boolean hasvalue = false; // 亿、万进位前有数值标记
        int len, n;
        len = NumStr.length();
        if (len > 15) {
            return "数值过大!";
        }
        for (int i = len - 1; i >= 0; i--) {
            if (NumStr.charAt(len - i - 1) == ' ') {
                continue;
            }
            n = NumStr.charAt(len - i - 1) - '0';
            if (n < 0 || n > 9) {
                return "输入含非数字字符!";
            }

            if (n != 0) {
                if (lastzero) {
                    RMBStr += HanDigiStr[0]; // 若干零后若跟非零值，只显示一个零
                    // 除了亿万前的零不带到后面
                    //if( !( n==1 && (i%4)==1 && (lastzero || i==len-1) ) )    // 如十进位前有零也不发壹音用此行
                }
                if (! (n == 1 && (i % 4) == 1 && i == len - 1)) { // 十进位处于第一位不发壹音
                    RMBStr += HanDigiStr[n];
                }
                RMBStr += HanDiviStr[i]; // 非零值后加进位，个位为空
                hasvalue = true; // 置万进位前有值标记
            }
            else {
                if ( (i % 8) == 0 || ( (i % 8) == 4 && hasvalue)) { // 亿万之间必须有非零值方显示万
                    RMBStr += HanDiviStr[i]; // “亿”或“万”
                }
            }
            if (i % 8 == 0) {
                hasvalue = false; // 万进位前有值标记逢亿复位
            }
            lastzero = (n == 0) && (i % 4 != 0);
        }
        if (RMBStr.length() == 0) {
            return HanDigiStr[0]; // 输入空字符或"0"，返回"零"
        }
        return RMBStr;
    }

    /**
     * 封装格式化的数据
     * @param var0
     * @param var1
     * @return
     */
    public static String join(Collection var0, String var1) {
        StringBuffer var2 = new StringBuffer();

        for(Iterator var3 = var0.iterator(); var3.hasNext(); var2.append((String)var3.next())) {
            if (var2.length() != 0) {
                var2.append(var1);
            }
        }

        return var2.toString();
    }

    public static String join(final Iterator<?> iterator, final String separator) {

        // handle null, zero and one elements before building a buffer
        if (iterator == null) {
            return null;
        }
        if (!iterator.hasNext()) {
            return "";
        }
        final Object first = iterator.next();
        if (!iterator.hasNext()) {
            @SuppressWarnings( "deprecation" ) // ObjectUtils.toString(Object) has been deprecated in 3.2
            final String result = ObjectUtils.toString(first);
            return result;
        }

        // two or more elements
        final StringBuilder buf = new StringBuilder(256); // Java default is 16, probably too small
        if (first != null) {
            buf.append(first);
        }

        while (iterator.hasNext()) {
            if (separator != null) {
                buf.append(separator);
            }
            final Object obj = iterator.next();
            if (obj != null) {
                buf.append(obj);
            }
        }
        return buf.toString();
    }
}
