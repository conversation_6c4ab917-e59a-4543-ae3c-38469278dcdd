<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ailk.newchnl.dao.MidHighEndRetentionAssessmentDao">

    <!-- 获取序列号 -->
    <select id="getSequence" resultType="long">
        select MID_HIGH_END_RETENTION_SEQ.nextval from dual
    </select>

    <!-- 插入中高端保有项目人员月度考评数据 -->
    <insert id="insert" parameterType="com.ailk.newchnl.entity.MidHighEndRetentionAssessment">
        INSERT INTO mid_high_end_retention_assessment (
            done_code,
            bill_month,
            role_name,
            ebc_job_number,
            assessment_coefficient,
            basic_cost_adjust_fee,
            adjust_fee,
            org_id,
            org_name,
            op_id,
            user_name,
            done_date,
            rec_status,
            agent_adjust_use_id,
            file_name,
            file_path
        ) VALUES (
            #{doneCode,jdbcType=NUMERIC},
            #{billMonth,jdbcType=VARCHAR},
            #{roleName,jdbcType=VARCHAR},
            #{ebcJobNumber,jdbcType=VARCHAR},
            #{assessmentCoefficient,jdbcType=DECIMAL},
            #{basicCostAdjustFee,jdbcType=DECIMAL},
            #{adjustFee,jdbcType=DECIMAL},
            #{orgId,jdbcType=VARCHAR},
            #{orgName,jdbcType=VARCHAR},
            #{opId,jdbcType=VARCHAR},
            #{userName,jdbcType=VARCHAR},
            #{doneDate,jdbcType=TIMESTAMP},
            #{recStatus,jdbcType=VARCHAR},
            #{agentAdjustUseId,jdbcType=VARCHAR},
            #{fileName,jdbcType=VARCHAR},
            #{filePath,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 查询中高端保有项目人员月度考评数据 -->
    <select id="query" parameterType="map" resultType="com.ailk.newchnl.entity.MidHighEndRetentionAssessment">
        SELECT
            done_code,
            bill_month,
            role_name,
            ebc_job_number,
            assessment_coefficient,
            basic_cost_adjust_fee,
            adjust_fee,
            org_id,
            org_name,
            op_id,
            user_name,
            done_date,
            rec_status,
            rec_status_list,
            agent_adjust_use_id,
            rec_status_approve,
            file_name,
            file_path
        FROM mid_high_end_retention_assessment
        WHERE rec_status != '-1'
        <if test="entity.billMonth != null and entity.billMonth != ''">
            AND bill_month = #{entity.billMonth}
        </if>
        <if test="entity.orgId != null and entity.orgId != ''">
            AND org_id = #{entity.orgId}
        </if>
        <if test="entity.recStatusList != null and entity.recStatusList != ''">
            AND rec_status IN
            <foreach collection="entity.recStatusList.split(',')" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="entity.ebcJobNumber != null and entity.ebcJobNumber != ''">
            AND ebc_job_number LIKE '%' || #{entity.ebcJobNumber} || '%'
        </if>
        ORDER BY done_date DESC
    </select>

    <update id="update" parameterType="map">
        UPDATE mid_high_end_retention_assessment
        <set>
            <if test="recStatus != null">rec_status = #{recStatus,jdbcType=VARCHAR},</if>
            <if test="agentAdjustUseId != null">agent_adjust_use_id = #{agentAdjustUseId},</if>
        </set>
        WHERE
        1=1
        <choose>
            <when test="doneCode != null and doneCode != ''">
                and done_code = #{doneCode}
            </when>
        </choose>
        <choose>
            <when test="billMonth != null">
                and bill_month = #{billMonth}
            </when>
        </choose>
        <choose>
            <when test="orgId != null">
                AND org_Id = #{orgId}
            </when>
        </choose>

    </update>

</mapper>