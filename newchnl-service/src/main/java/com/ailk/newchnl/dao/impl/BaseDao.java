/*
 * $Id: BaseDao.java,v 1.3 2014/07/25 08:20:40 fuqiang Exp $
 *
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.dao.impl;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.ailk.newchnl.mybatis.pagination.PageParameter;

/**
 * <AUTHOR>
 * @version $Id: BaseDao.java,v 1.3 2014/07/25 08:20:40 fuqiang Exp $
 * Created on 2014年7月19日 下午2:20:07
 */
public interface BaseDao <T> {
	/**
	 * 通过主键获取一条记录
	 * @param t
	 * @return
	 */
	public T get(T t);

	/**
	 * 查询多条记录
	 * @param t
	 * @return
	 */
	public List<T> query(@Param("entity")T t);
	
	/**
	 * 按分页方式查询多条记录
	 * @param t
	 * @param page
	 * @return
	 */
	public List<T> query(@Param("entity")T t, @Param("page") PageParameter page);
	
	/**
	 * 删除一条记录
	 * @param t
	 * @return
	 * @throws Exception
	 */
	public int delete(T t) throws Exception;
	
	/**
	 * 修改记录
	 * @param t
	 * @return
	 * @throws Exception
	 */
	public int update(T t) throws Exception;
	
	/**
	 * 插入一条记录
	 * @param t
	 * @return
	 * @throws Exception
	 */
	public int insert(T t) throws Exception;
	
	/**
	 * 获取序列好
	 * @return
	 */
	public Long getSequence();
}

