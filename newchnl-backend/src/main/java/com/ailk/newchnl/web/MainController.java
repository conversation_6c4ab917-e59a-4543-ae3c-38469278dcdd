/*
 * $Id: MainController.java,v 1.47.2.1 2015/06/03 02:32:26 fuqiang Exp $
 *
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.web;

import com.ailk.newchnl.entity.ChannelSysBaseType;
import com.ailk.newchnl.entity.SPrivData;
import com.ailk.newchnl.entity.menu.*;
import com.ailk.newchnl.entity.resource.ChnlResModelDefinition;
import com.ailk.newchnl.entity.resource.ChnlResType;
import com.ailk.newchnl.service.ChannelSoaService;
import com.ailk.newchnl.service.ChannelSysBaseTypeService;
import com.ailk.newchnl.service.ComboOptionService;
import com.ailk.newchnl.util.ChannelSysBaseTypeUtil;
import com.ailk.newchnl.util.ChannelUtil;
import com.ailk.newchnl.web.controller.BaseController;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @version $Id: MainController.java,v 1.47.2.1 2015/06/03 02:32:26 fuqiang Exp $
 * Created on 2014年6月12日 下午4:46:58
 */

@Controller
@RequestMapping("/")
public class MainController extends BaseController {

	private static final Logger logger = LoggerFactory.getLogger(MainController.class);

	@Resource
	private ChannelSysBaseTypeService channelSysBaseTypeService;

	@Resource
	private ChannelSoaService channelSoaService;

	@Resource
	private ComboOptionService comboOptionService;

	@RequestMapping(value="/")
	public ModelAndView main(HttpServletRequest request){
		//登陆成功后设置 缓存  channel_sys_base_type
		//获取全部的 channel_sys_base_type
		try{
			Map<String,List<ChannelSysBaseType>> sysBaseTypeMap = new HashMap<String,List<ChannelSysBaseType>>();
			List<ChannelSysBaseType> channelSysBaseTypeList= ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(null, null, null, null);//channelSysBaseTypeService.getChannelSysBaseTypeList(null, null, null, null);
			for(ChannelSysBaseType channelSysBaseType : channelSysBaseTypeList){
				if(sysBaseTypeMap.get(""+channelSysBaseType.getCodeType()) == null){
					sysBaseTypeMap.put(""+channelSysBaseType.getCodeType(), new ArrayList<ChannelSysBaseType>());
				}
				sysBaseTypeMap.get(""+channelSysBaseType.getCodeType()).add(channelSysBaseType);
			}
			JSONObject json = JSONObject.fromObject(sysBaseTypeMap);
			synchronized(ChannelUtil._CHANNEL_SYS_BASE_TYPE){
				ChannelUtil._CHANNEL_SYS_BASE_TYPE = json.toString();
			}

			List<ChnlResModelDefinition> chnlResModelDefinitionList = comboOptionService.queryChnlResModelDefinition(null);
			JSONArray jsonArray = JSONArray.fromObject(chnlResModelDefinitionList);
			synchronized(ChannelUtil._CHNL_RES_MODEL_DEFINITION){
				ChannelUtil._CHNL_RES_MODEL_DEFINITION = jsonArray.toString();
			}

			List<ChnlResType> chnlResTypeList = comboOptionService.queryChnlResType(null, null);
			jsonArray = JSONArray.fromObject(chnlResTypeList);
			synchronized(ChannelUtil._CHNL_RES_TYPE){
				ChannelUtil._CHNL_RES_TYPE = jsonArray.toString();
			}
		}catch(Exception e){
			logger.error("", e);
		}

		/**
		 * 测试时使用
		 */
		/*SPrivData sPrivData = new SPrivData();
		sPrivData.setOpId(690313L);
		sPrivData.setOrgId(20167L);
		sPrivData.setOpName("朱旼琰");
		sPrivData.setUserName("CMSC0039");
		sPrivData.setOrgName("崇明分公司");
		ChannelUtil.setSessionSPrivDataInfo(request, sPrivData);*/

		SPrivData sPrivData = new SPrivData();
		sPrivData.setOpId(713385L);
		sPrivData.setOrgId(6L);//502417
		sPrivData.setOpName("殷酉亮");
		sPrivData.setUserName("YINYOULIANG");
		sPrivData.setOrgName("市场运营中心");
		ChannelUtil.setSessionSPrivDataInfo(request, sPrivData);




		ModelAndView view = new ModelAndView();
		view.setViewName("/main");
		return view;
	}

	@RequestMapping(value="/login")
	public ModelAndView login(HttpServletRequest request){
		ModelAndView view = new ModelAndView();
		view.setViewName("/login");
		return view;
	}

	@RequestMapping(value="/welcome")
	public ModelAndView welcome(HttpServletRequest request){
		ModelAndView view = new ModelAndView();
		view.setViewName("/welcome");
		return view;
	}

	@RequestMapping(value="/menu")
	@ResponseBody
	public Map getMenu(HttpServletRequest request){
		Map result = new HashMap();
		Menu menu = new Menu();
		SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
		try {
			logger.info("===============获取菜单信息.");
			Map<String, Object> busiParams = new HashMap<String,Object>();
			busiParams.put("operatorId",  ""+sPrivData.getOpId());
			busiParams.put("domainId", "3");
			busiParams.put("moduleType",  "1");

			String jsonString = channelSoaService.call("loadMenu", busiParams, sPrivData);
			logger.debug("---------------"+jsonString);

			@SuppressWarnings({ "unchecked", "deprecation" })
			List<Function> functionList = (List<Function>) JSONArray.toList( JSONObject.fromObject(jsonString).getJSONArray("secFuncList"), Function.class);
			Collections.sort(functionList, new Comparator<Function>() {
				public int compare(Function o1, Function o2) {
					if(o1.getFuncId() != null && o2.getFuncId() != null){
						return o1.getFuncId().compareTo(o2.getFuncId());
					}
					return 0;
				}
			});
			for(Function function : functionList){
				if(function.getParentfuncId().equals("-1")){
					MenuItemLevel1 itemLevel1 = new MenuItemLevel1(function.getFuncId(), function.getParentfuncId(), "", function.getFuncName());
					for(Function function2 : functionList){
						if(function2.getParentfuncId().equals(function.getFuncId())){
							MenuItemLevel2 itemLevel2 = new MenuItemLevel2(function2.getFuncName());
							for(Function function3 : functionList){
								if(function3.getParentfuncId().equals(function2.getFuncId())){
									MenuItemLevel3 itemLevel3 = new MenuItemLevel3(function3.getFuncId(), function3.getFuncName(), function3.getFuncUrl());
									itemLevel2.addMenuItemLevel3(itemLevel3);
								}
							}
							itemLevel1.addMenuItemLevel2(itemLevel2);
						}
					}
					menu.addMenuItemLevel1(itemLevel1);
				}
			}
			result.put("status", 0);
			result.put("data", menu);

		} catch (Exception e) {
			String msg = "获取菜单信息出错!";
			result.put("status", 1);
			result.put("msg", msg + e.getMessage());
			logger.error(msg, e);
		}


		/*String menus[] = {"1301,-1,合作方",
                "1302,-1,业务",
                "1303,-1,资源",
                "1304,-1,营销活动(暂未开放)",
                "1305,-1,酬金(暂未开放)",
                "1306,-1,考核(测试)",
                "1307,-1,系统(暂未开放)",
                "1308,-1,激励积分",
                "1309,-1,店员积分(暂未开放)",
				"1311,-1,审批及查询",
				"1312,-1,稽核",
				"30503,1305,服务费管理",
				"3050301,30503,合作厅服务费录入,agentAward//page/serviceFeeAdd",
				"3050302,30503,合作厅服务费管理,agentAward/page/serviceFeeManager",
				"3050303,30503,加盟营业厅服务费考核录入,agentAward/page/serviceFeeAppraiseAdd",
				"3050304,30503,加盟营业厅服务费考核管理,agentAward/page/serviceFeeAppraiseManage",
				"3050305,30503,加盟营业厅分类信息录入,agentAward/page/leagueNodeClassifyInfoAdd",
				"3050306,30503,加盟营业厅分类信息管理,agentAward/page/leagueNodeClassifyInfoManage",
				"3050307,30503,终端特许经营考核录入,manageCheck/manageCheckAdd",
				"3050308,30503,终端特许经营考核管理,manageCheck/manageCheckOperate",
				"3050309,30503,在线公司扣罚系数录入,agentAward/page/penaltyCoefficientsAdd",
				"3050310,30503,在线公司考核得分录入,agentAward/page/companyAssessmentScoresAdd",
				"3050311,30503,铁通看管项目考核打分录入,agentAward/page/tietongItemScoresAdd",
				"3050312,30503,业务集中稽核考核录入,agentAward/page/businessAuditAssessAdd",
				"3050313,30503,业务集中稽核考核评分查询,agentAward/page/businessAuditAssessQuery",
				"3060205,30503,网格信息维护,gridDirectSelling/serviceFeeAssMaintenance",
				"3060206,30503,网格直销服务费考核管理,gridDirectSelling/serviceFeeAssManage",
				"3060207,30503,加盟营业厅营销支撑费考核录入,agentAward/page/assessFeeAppraiseAdd",
				"3060208,30503,加盟营业厅营销支撑费考核管理,agentAward/page/assessFeeAppraiseManage",
				"3060209,30503,社会渠道人员补贴核算,agentAward/page/channelPeopleAccounting",
				"3080403,30503,手机卖场店员激励签收数据统计,agentAward/page/mobileStoreClerksDateOfJLQS",
				"3050320,30503,第三方行销项目月度目标系数录入,agentAward/page/businessThirdProjectRatioAdd",
				"3050321,30503,第三方行销项目月度目标系数管理,agentAward/page/businessThirdProjectRatioManage",
				"3050322,30503,第三方行销项目达量系数管理,agentAward/page/businessThirdProjectReachManage",
				"3050323,30503,熔断名单,agentAward/page/channelFuseItem",
				"3050324,30503,融合刚性目标,agentAward/page/channelFuseedRigidTarget",
				"3050325,30503,第三方行销费用考核录入,agentAward/page/businessThirdAssessmentAdd",
				"3050326,30503,第三方行销人员基薪,agentAward/page/businessThirdPersonMarket",
				"3050327,30503,第三方行销项目支撑费录入,agentAward/page/threeSupportFeeAdd",
				"3050328,30503,第三方行销项目支撑费管理,agentAward/page/threeSupportFeeManage",
				"3050329,30503,第三方行销人员考核录入,agentAward/page/busiThirdXXAssessAdd",
                "3050330,30503,第三方行销人员考核管理,agentAward/page/busiThirdXXAssessManage",
				"3050331,30503,第三方行销费用考核管理,agentAward/page/businessThirdAssessmentManage",
				"3050332,30503,第三方行销人员费用录入,agentAward/page/threeNodeNameFeeAdd",
				"3050333,30503,第三方行销人员费用管理,agentAward/page/threeNodeNameFeeManage",
				"3050336,30503,在线公司专席结算录入,agentAward/page/companySpecSettlementAdd",
				"3050337,30503,融合业务零产能,agentAward/page/busiFuseProAdd",
				"3050338,30503,融合业务零产能管理,agentAward/page/busiFuseProManage",
				"3050339,30503,中高端保有项目人员月度考评录入,agentAward/page/midHighEndRetentionAssessmentAdd",

//                "3050325,30503,第三方行销项目专项费用录入,agentAward/page/businessThirdSpecFeeAdd",
//                "3050326,30503,店员激励透传信息管理,agentAward/page/channelInfoManageToDYJL",
//                "3050327,30503,渠道店员透传激励实施信息,agentAward/page/channelTracksInfoToDYJL",
				"30101,1301,合作方信息管理",
                "3010101,30101,合作方申请管理,agent/agentImportManager",
                "3010102,30101,合作方信息查询,agent/agentInfoQuery",
                "3010103,30101,合作方信息修改,agent/agentInfoModify",
                "3010104,30101,合作方信息审批,agent/agentInfoCheack",
                "3010105,30101,合作方预销户,agent/agentPreAway",
                "3010106,30101,合作方销户,agent/agentAway",
                "3010107,30101,合作方申请信息修改,agent/agentApplicationInfoModify",
                "3010108,30101,合作方批量录入,entityBatchSyncFile/agentInfoBatchImport",
                "3010109,30101,合作方资金账户管理,agentFund/agentApplyFundManage",
                "3010110,30101,合作方资金账户查询,agentFund/agentApplyFundQuery",
                "3010111,30101,合作方预占号码上限管理(测试),agentInfoQuery/agentNumberLimit",
                "3010112,30101,渠道管理人员服务号码管理(测试),agentInfoQuery/chnlMgntNum",
                "3010113,30101,实体批量操作,agentInfoQuery/entityInfoBatchModifyImport",
                "3010114,30101,终端机型和合作方对应关系(测试),agentInfoQuery/channelMatchTerminal",
                "30102,1301,网点信息管理",
                "3010201,30102,网点信息查询,node/showPage?codeId=99&channelEntityId=&operateType=",
                "3010202,30102,网点信息录入,node/showPage?codeId=100&channelEntityId=&operateType=",
                "3010203,30102,商圈管理,commerceCircle/list",
                "3010204,30102,集中地管理,channelCentrally/channelCentrally",
                "3010205,30102,终端支撑网点管理,channelSupportOffce/channelSupportOffce",
                "3010206,30102,终端支撑手机卖场管理,channelSupportPhoneStoreController/channelSupportPhoneStoreController",
                "3010207,30102,客户经理管理,accountManager/managePage",
                "3010208,30102,客户经理批量导入,accountManager/batchImportPage",
                "3010209,30102,网点批量录入,entityBatchSyncFile/showNodeBatchImport?nodeKind=",
                "3010210,30102,审批代理小区,node/showPage?codeId=97&channelEntityId=&operateType=",
                "3010211,30102,自营厅批量录入,entityBatchSyncFile/showNodeBatchImport?nodeKind=1",
                "3010212,30102,卡号分离短信激活门店信息录入(测试),chnlSmsActiveNode/chnlSmsActiveNodeAdd",
                "3010213,30102,卡号分离短信激活门店信息修改(测试),chnlSmsActiveNode/chnlSmsActiveNodeModify",
                "3010214,30102,卡号分离短信激活门店信息查询(测试),chnlSmsActiveNode/chnlSmsActiveNodeQuery",
                "3010215,30102,客户经理管理(测试),accountManager/managePage",
                "3010216,30102,客户经理批量导入(测试),accountManager/batchImportPage",
                "3010217,30102,网点年检录入(测试),chnlNodeYearCheckInfo/showChnlNodeYearCheckInfo",
                "3010218,30102,网点星级考评报表(测试),chnlNodeStarCheck/showChnlNodeStarCheck",
                "3010219,30102,网点年检查询(测试),chnlNodeYearCheckInfo/showChnlNodeYearCheckInfoQuery",
                "3010220,30102,门店基站注册信息查询(测试),node/nodeRegisterBaseInfoQuery",
                "3010221,30102,实名制拍照软件信息管理(测试),chnlRealNameSoftwareInfo/chnlRealNameSoftwareInfoQuery",
                "3010222,30102,实名制信息管理,channelNodeReaName/realName",
                "3010223,30102,网点额度调整,nodeLimitAdjustController/showNodeLimitAdjust",
                "3010224,30102,网点资质调整(测试),nodeAptitideAdjust/showNodeAptitideAdjust",
                "3010225,30102,网点资质调整审核(测试),nodeAptitideAdjust/showNodeAptitideAdjustValidate",
                "3010226,30102,网点资质调整相关人员设置(测试),channelNodeStarAdjManager/showAptitideAdjustOperatorManager",
                "3010227,30102,手机专卖店和授权代理店销售跟踪报表(测试),chnlNodeTrack/showChnlNodeTrack",
                "3010228,30102,专营店人员系统验证录入(测试),channelZydPhoneCheck/showChannelZydPhoneCheck",
                "3010229,30102,网点设备配置,deviceManage/page4Manage",
                "30103,1301,罚金信息管理",
                "3010301,30103,罚金查询,agentBustFunctionDtl/agentFineQuery",
                "30104,1301,实体对应关系管理",
                "3010401,30104,实体对应关系,channelEntityMatchOrg/channelEntityMatchOrg",
                "3010402,30104,批量实体对应关系,channelEntityMatchOrg/entityMatchOrgBatch",
                "3010403,30104,实体批量进度查询,entityBatchSyncFile/showEntityBatchSyncFile",
                "30105,1301,保证金管理(测试)",
                "3010501,30105,保证金管理(测试),agentFundManager/agentFund",
                "3010502,30105,保证金录入(测试),agentFundManager/agentFundIn",
                "3010503,30105,保证金支出(测试),agentFundManager/agentFundManagerOutPage",
                "3010504,30105,保证金历史记录信息(测试),agentFundManager/agentFundHistory",
                "3010505,30105,合作方各分公司保证金负责人录入(测试),agentFundCeo/agentCeoInfoImport",
                "30106,1301,罚金信息管理(测试)",
                "3010601,30106,罚金查询(测试),agentBustFundDtl/agentFineQuery",
                "3010602,30106,罚金录入(测试),agentBustFundDtl/agentBustFundDtlAdd",
                "3010603,30106,罚金维护(测试),agentBustFundDtl/agentBustFundDtlFrameset",
                "3010604,30106,罚金批量导入(测试),agentBustFundDtl/agentFineUpload",
                "30107,1301,合作厅及外包厅信息管理(测试)",
                "3010701,30107,合作厅及外包厅可变成本录入(测试),telecomCooperationManager/teleCoorperationCost",
                "30108,1301,自营厅信息管理(测试)",
                "3010801,30108,自营厅可变成本录入(测试),channelNodeMonthCost/showChannelNodeMonthCost",
                "30109,1301,代理查询(测试)",
                "3010901,30109,代理查询公布信息点击次数(测试),proxyQuery/proxyQueryPublicInfoClickNum",
                "3010902,30109,密码狗绑定MAC日志查询(测试),proxyQuery/passwdDogMACBindLogQueryTest",
				//网格承包商管理
				"30110,1301,网格承包商管理(测试)",
				"3011001,30110,承包商资质名单管理(测试),contractorManager/contractorQualificationListManager",
				"3011002,30110,微格对应关系绑定(测试),contractorManager/smallGridBinding",
				"3011003,30110,微格对应关系查询(测试),contractorManager/smallGridQuery",

				"30201,1302,报表管理",
                "3020101,30201,代理商实名制登记业务量,report/report/realNameAmount",
                "3020102,30201,代理商实名制登记业务清单查询,report/report/realNameAmountDetail",
                "3020103,30201,订单汇总表,report/report/channelAssignQuery",
                "3020104,30201,订单明细表,report/report/orderDetailQuery",
                "3020105,30201,返卡率考核(未返卡),report/report/weifankaQuery",
                "3020106,30201,返卡率考核(已返卡),report/report/yifankaQuery",
                "3020107,30201,返卡率月统计,report/report/fankatjQuery",
                "3020108,30201,返卡提醒,report/report/fankatxQuery",
                "3020109,30201,返信息统计,report/report/backInfoStatisticQuery",
                "3020110,30201,付费统计,report/report/payQuery",
                "3020111,30201,清单查询,report/report/listOfitemsQuery",
                "3020112,30201,数据业务清单,report/report/dataBusiListQuery",
                "3020113,30201,未返卡统计,report/report/weiBackCardStatisticQuery",
                "30202,1302,沉默网点",
                "3020201,30202,任务查询页面,slienceNode/taskFindPage",
                "3020202,30202,任务报表,slienceNode/taskReport",
                "3020203,30202,沉默网点分公司负责人配置,slienceNode/slienceInspectorCompany",
                "30203,1302,充值卡领用额度管理",
                "3020301,30203,充值卡领用额度管理,channelAgentLimitNums/showOrderNumMgrChange",
                "3020302,30203,合作方领用额度查询,channelAgentLimitNums/showOrderNumMgrSearch",
                "30301,1303,白卡管理",
                "3030101,30301,白卡类型修改,whiteCardManage/page4Modify",
                "3030102,30301,白卡入库,whiteCardManage/page4Storage",
                "3030103,30301,白卡迁移,whiteCardManage/page4Transfer",
                "3030104,30301,白卡删除,whiteCardManage/page4Delete",
                "3030105,30301,白卡查询,whiteCardManage/page4Query",
                "30302,1303,冷号管理",
                "3030201,30302,冷号查询,inactivePhoneManage/page4Query",
                "3030202,30302,冷号预处理,inactivePhoneManage/page4Clean",
                "3030203,30302,冷号卡迁移,inactivePhoneManage/page4Transfer",
                "3030204,30302,冷号卡删除,inactivePhoneManage/page4Delete",
                "3030205,30302,冷号卡修改,inactivePhoneManage/page4Modify",
                "3030206,30302,冷号卡入库,inactivePhoneManage/page4Storage",
                "3030207,30302,自营店迁移,inactivePhoneManage/page4TransferAuto",
                "30303,1303,有价卡管理",
                "3030301,30303,有价卡查询,busiCardManage/page4Query",
                "3030302,30303,有价卡迁移,busiCardManage/page4Transfer",
                "3030303,30303,有价卡修改,busiCardManage/page4Modify",
                "3030304,30303,有价卡入库,busiCardManage/page4Storage",
                "3030305,30303,自营店迁移,busiCardManage/page4TransferAuto",
                "3030306,30303,有价卡删除,busiCardManage/page4Delete",
                "30304,1303,有号卡管理",
                "3030401,30304,有号卡查询,resInactivePhoneManage/page4Query",
                "3030402,30304,有号卡迁移,resInactivePhoneManage/page4Transfer",
                "3030403,30304,有号卡删除,resInactivePhoneManage/page4Delete",
                "3030405,30304,有号卡入库,resInactivePhoneManage/page4Storage",
                "3030406,30304,自营店迁移,resInactivePhoneManage/page4TransferAuto",
                "30304304,30304,有号卡修改,resInactivePhoneManage/page4Modify",
                "30305,1303,资源报表",
                "3030501,30305,配号统计,report/report/assigntotalQuery",
                "3030502,30305,有价卡分配,report/report/pricecardQuery",
                "3030503,30305,有号卡分配,report/report/numbercardQuery",
                "3030504,30305,冷号分配,report/report/singlecardQuery",
                "30601,1306,其他网点业务考核(测试)",
                "3060101,30601,业务量查询(测试),agentBusiAmount/agentBusiAmountQuery",
                "3060102,30601,业务量录入(测试),agentBusiAmount/agentBusiAmountAdd",
                "30602,1306,加盟社会店业务考核(测试)",
                "3060201,30602,业务量查询(测试),agentBusiAmount/feeAssessBusiQuery",
                "3060202,30602,业务量录入(测试),agentBusiAmount/feeAssessBusiAdd",
                "30603,1306,加盟营业厅业务考核(测试)",
                "3060301,30603,业务量查询(测试),agentBusiAmount/joinTheBusinessAssessQuery",
                "3060302,30603,业务量录入(测试),agentBusiAmount/joinTheBusinessAssessAdd",
                "30604,1306,手机卖场业务考核(测试)",
                "3060401,30604,业务量查询(测试),agentBusiAmount/mobileStoreAssessQuery",
                "3060402,30604,业务量录入(测试),agentBusiAmount/mobileStoreAssessAdd",
                "30605,1306,手机专卖店业务考核(测试)",
                "3060501,30605,业务量查询(测试),agentBusiAmount/cellPhoneStoreAssessQuery",
                "3060502,30605,业务量录入(测试),agentBusiAmount/cellPhoneStoreAssessAdd",
                "30606,1306,授权代理店业务考核(测试)",
                "3060601,30606,业务量查询(测试),agentBusiAmount/authorizedAgencyAssessQuery",
                "3060602,30606,业务量录入(测试),agentBusiAmount/authorizedAgencyAssessAdd",
                "30607,1306,考核报表(测试)",
                "3060701,30607,财务报表(测试),report/assess/finace",
                "3060702,30607,差错费统计(测试),report/assess/mistake",
                "3060703,30607,发展统计分析(测试),report/assess/developstat",
                "3060704,30607,放号报表(测试),report/assess/fanghao",
                "3060705,30607,卡号发展进度查询(测试),report/assess/cardnodevelopprogress",
                "3060706,30607,品牌业务报表(测试),report/assess/planservicestat",
                "3060707,30607,渠道业务报表(测试),report/assess/service",
                "3060708,30607,网点白卡写卡品牌明细表(测试),report/assess/whiteWritecardDetail",
                "3060709,30607,网点写卡激活报表(测试),report/assess/nodeActive",
                "3060710,30607,网点业务统计(测试),report/assess/nodeBusinessStatics",
                "3060711,30607,业务量查询(测试),report/assess/feeBusiQueryReport",
                "3060712,30607,业务量排名统计(测试),report/assess/feeBusiOrderReport",
                "3060713,30607,业务量统计(测试),report/assess/access",
                "3060714,30607,专营店业务量排名统计(测试),report/assess/zydfeeBusiOrderReport",
                "30608,1306,巡检考核(测试)",
                "3060801,30608,巡检员信息录入(测试),channelInspectorInfo/xjinfoImport",
                "3060802,30608,巡检指标管理(测试),channelNodePatrolIndex/channelNodePatrolIndex",
                "3060803,30608,巡检员网点分配录入(测试),channelInspectorInfo/inspectorNodeRelationImport",
                "3060804,30608,巡检员网点分配查询(测试),channelInspectorInfo/inspectorNodeRelationQuery",
                "3060805,30608,巡检员评分查询(测试),channelInspecNode/inspectorNodeGradeQuery",
                "3060806,30608,全公司评分查询(测试),channelInspecNode/nodeGradeQuery",
                "3060807,30608,管理员评分查询(测试),channelInspecNode/adminNodeGradeQueryInfo",
                "3060808,30608,评分审核(测试),channelInspectOpLog/channelInspectOpLog",
                "3060809,30608,巡检模板管理(测试),channelNodePatrolTemplate/xjtempletesmanage",
                "3060810,30608,GIS地图展示(总公司)(测试),gISPatrolGrid/gisFrameTest",
                "3060811,30608,GIS地图展示(分公司)(测试),gISPatrolGrid/gisFrameTestcpy",
                "3060812,30608,GIS地图展示(人员考核范围)(测试),gISPatrolGrid/gisFrameTestxj",
                "3060813,30608,GIS巡检网点考核汇总(测试),gISPatrolGrid/gisPatrolGridSum",
                "30609,1306,服务考核(测试)",
                "3060901,30609,赢回活动配合度系数录入(测试),templeRelManager/activityRatioImport",
                "30610,1306,渠道运营管理",
                "3061001,30610,渠道运营指标导入,indicatorMonthManager/indicatorMonthImport",
                "3061002,30610,渠道合作协议实施跟踪,indicatorMonthManager/channelAgentProTracks",
                "3061003,30610,",
                "30801,1308,积分管理",
                "3080101,30801,积分冻结,points/page/pointsFreeze",
                "3080102,30801,积分解冻,points/page/pointsDefrost",
                "30802,1308,积分账户管理",
                "3080201,30802,积分账户申请,points/page/pointsAccountApply",
                "3080202,30802,账户申请审批,points/page/pointsAccountApprove",
                "3080203,30802,积分账户销户,points/page/pointsAccountCancel",
                "30803,1308,积分兑换",
                "3080301,30803,积分酬金支付,points/page/pointsRewardPay",
                "3080302,30803,积分兑换,points/page/pointsExchange",
                "3080303,30803,积分兑换回退,points/page/pointsExchangeBack",
                "3080304,30803,积分预扣,points/page/pointsDeficit",
                "3080305,30803,积分预扣审批,points/page/pointsDeficitApprove",
                "30804,1308,积分调整",
                "3080401,30804,积分调整,points/page/pointsAdjust",
                "30805,1308,积分报表",
                "3080501,30805,积分兑换明细报表,points/page/pointsExchangeDtlReport",
                "3080502,30805,积分变化明细表,points/page/pointsChangeDtlReport",
				"31110,1311,审批人员维护",
				"3111101,31110,审批人员维护,agentAdjustFeeCller/deployMenue",
				"31111,1311,审批待办",
				"3111111,31111,审批待办,agentAdjustFeeCller/interiorbuildQuery",
				"31112,1311,已办查询",
				"3111112,31112,已办查询,agentAdjustFeeCller/doneMenue",
				"3111113,31112,驳回数据查询,agentAdjustFeeCller/errorDoneMenue",

				"31113,1311,第三方直销审批待办",
				"3111301,31113,第三方行销人员考核审批,agentAdjustFeeCller/busiThirdXXAssessApprove",
				"3111302,31113,第三方行销费用审批,agentAdjustFeeCller/businessThirdAssessmentApprove",
				"3111303,31113,第三方行销人员费用审批,agentAdjustFeeCller/threeNodeNameApprove",
				"3111304,31113,第三方行销项目支撑费考核审批,agentAdjustFeeCller/interiorbuildQuery",
        		"31200,1312,电费稽核",
				"3120001,31200,角色管理,processPersonRole/index",
				"3120002,31200,电费稽核任务查询,busiFallFee/pageRoute.html?pageType=1",
				"3120003,31200,电费稽核信息查询,busiFallFee/pageRoute.html?pageType=5",
				"30509,1305,酬金分析",
				"3080402,30509,属地渠道费用汇总报表,ChannelDependencySumFee/page/channelDependencySumFee"
		};

        List<Function> functionList = new ArrayList<Function>();
        for(int i=0;i<menus.length;i++){
            String menu_[] = menus[i].split(",");
            if(menu_.length >= 3){
                Function function = new Function();
                function.setFuncId(menu_[0]);
                function.setParentfuncId(menu_[1]);
                function.setFuncName(menu_[2]);
                function.setFuncUrl(menu_.length >= 4 ? menu_[3] : "");
                functionList.add(function);
            }
        }
        for(Function function : functionList){
            if(function.getParentfuncId().equals("-1")){
                MenuItemLevel1 itemLevel1 = new MenuItemLevel1(function.getFuncId(), function.getParentfuncId(), "", function.getFuncName());
                for(Function function2 : functionList){
                    if(function2.getParentfuncId().equals(function.getFuncId())){
                        MenuItemLevel2 itemLevel2 = new MenuItemLevel2(function2.getFuncName());
                        for(Function function3 : functionList){
                            if(function3.getParentfuncId().equals(function2.getFuncId())){
                                MenuItemLevel3 itemLevel3 = new MenuItemLevel3(function3.getFuncId(), function3.getFuncName(), function3.getFuncUrl());
                                itemLevel2.addMenuItemLevel3(itemLevel3);
                            }
                        }
                        itemLevel1.addMenuItemLevel2(itemLevel2);
                    }
                }
                menu.addMenuItemLevel1(itemLevel1);
            }
        }
        result.put("status", 0);
        result.put("data", menu);*/
		return result;
	}

}
