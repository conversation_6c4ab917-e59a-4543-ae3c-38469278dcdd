<!--
 * 
 * $Id: feeMistakeAdd.jsp,v 1.1 2015/05/11 06:42:08 fuqiang Exp $
 * Copyright 2015 Asiainfo Technologies(China),Inc. All rights reserved.
 *
-->
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
<title>差错费管理录入</title>
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn_bricks.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body>
	<!-- here we will place the layout --->
	<div id="layoutObj"></div>
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_json.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn.js"></script>     
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/module/archive/node/item.js"></script>
<script>
	var _width = $(document).width();
	var _height = $(document).height();
	$("#layoutObj").css("height", document.documentElement.clientHeight);
	BUI.use('module/fee/feeMistakeAdd',function(FeeMistakeAdd){
		new FeeMistakeAdd();
	});
</script>
</html>