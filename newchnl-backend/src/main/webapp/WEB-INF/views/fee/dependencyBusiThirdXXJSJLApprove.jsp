<%--
 * 
 * $Id: busiThirdXXAssessApprove.jsp,v 1.1 2023/11/20 02:56:36 chefh Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
<head>
	<title>属地第三方行销即时激励审批</title>
	<link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
	<link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
	<link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
</head>
<body>
   <!-- 容器开始 -->
   <div class="container">

	   <!-- operate 开始 -->
	   <div id="tb">
		   <div>
			   <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true"
				  id="approveBtn">审批通过</a>
			   <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true"
				  id="disApproveBtn">审批驳回</a>
			   <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true"
				  id="downLoadBtn">附件下载</a>
		   </div>
	   </div>
	   <!-- operate 结束 -->

	   <!-- 数据显示开始 -->
	   <div class="row">
		   <table id="queryMyGrid" title="查询结果"
				  data-options="pageSize:10,height:350,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
			   <thead>
			   <tr>
				   <th data-options="field:'ck',checkbox:true"></th>
				   <th data-options="field:'doneCode',hidden:false,align:'center',width:120">序列号</th>
				   <th data-options="field:'nodeId',hidden:true,align:'center',width:120">nodeId</th>
                   <th data-options="field:'billMonth',align:'center',width:160">时间</th>
                   <th data-options="field:'orgName',align:'center',width:100">属地分公司</th>
                   <th data-options="field:'nodeName',align:'center',width:200">格内小组长网点名称（兑付人）</th>
                   <th data-options="field:'gridName',align:'center',width:120">网格名称</th>
                   <th data-options="field:'nodeFee',align:'center',width:120">金额</th>
				   <th data-options="field:'approveStatus',align:'center',width:120">处理环节</th>
                   <th data-options="field:'fileName',hidden:true,align:'center',width:200">附件名称</th>
                   <th data-options="field:'filePath',hidden:true,align:'center',width:120">附件路径</th>
                   <th data-options="field:'agentAdjustUseId',align:'center',width:120">审批人Id</th>
                   <th data-options="field:'agentAdjustUseName',align:'center',width:120">当前审批人</th>
			   </tr>
			   </thead>
		   </table>
	   </div>
	   <fieldset class="span24" style="margin-top: 50px" id="areaList">
		   <legend>审批流程</legend>
		   <div class="row">
			   <div class="control-group span8">
				   <label class="control-label">审批流程：</label>
				   <div class="controls">
					   <select type="text" class="easyui-validatebox" style="width: 150px"
							   data-options="panelHeight:'200',editable:true,valueField:'id',textField:'text'"
							   name="flowAjudt" id="flowAjudt">
						   <Option value="1">属地内部流程</Option>
					   </select>
				   </div>
			   </div>
			   <div class="control-group span8">
				   <label class="control-label">属地二级经理：</label>
				   <div class="controls">
					   <select type="text" class="easyui-validatebox" style="width: 150px"
							   data-options="panelHeight:'200',editable:true,valueField:'id',textField:'text'"
							   name="flowThreeAmader" id="flowThreeAmader">
						   <Option value="3">属地分管二级经理</Option>
					   </select>
				   </div>
			   </div>
			   <div class="control-group span8">
				   <label class="control-label">属地分管二级审批人：</label>
				   <div class="controls">
					   <select type="text" class="easyui-validatebox" style="width: 150px"
							   data-options="panelHeight:'200',editable:true,valueField:'id',textField:'text'"
							   name="flowThreeAmaderName" id="flowThreeAmaderName">
					   </select>
				   </div>
			   </div>
		   </div>
	   </fieldset>
	   <!-- 数据显示结束 -->
   </div>
   <!-- 容器结束 -->
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script>
	BUI.use('module/fee/dependencyBusiThirdXXJSJLApprove', function (dependencyBusiThirdXXJSJLApprove) {
		new dependencyBusiThirdXXJSJLApprove();
	});
</script>
</html>