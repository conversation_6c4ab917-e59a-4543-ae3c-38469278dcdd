<%--
 *
 * $Id: busiThirdXXAssessApprove.jsp,v 1.1 2023/11/20 02:56:36 chefh Exp $
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 *
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
    <head>
        <title>第三方行销网点名称调整费审批</title>
        <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
        <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
    </head>
<body>
<!-- 容器开始 -->
<div class="container">

    <!-- operate 开始 -->
    <div id="tb">
        <div>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true"
               id="approveBtn">审批通过</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true"
               id="disApproveBtn">审批驳回</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="icon-excel:'icon-save',plain:true"
               id="downLoadBtn">附件下载</a>
        </div>
    </div>
    <!-- operate 结束 -->

    <!-- 数据显示开始 -->
    <div class="row">
        <table id="queryMyGrid" title="查询结果"
               data-options="pageSize:10,height:350,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
            <thead>
            <tr>
<%--                <th data-options="field:'ck',checkbox:true"></th>--%>
                <th data-options="field:'faceId',hidden:false,align:'center',width:120">流水编号</th>
                <th data-options="field:'channelEntityId',hidden:true,align:'center',width:120">网点Id</th>
                <th data-options="field:'theMonth',align:'center',width:100">月份</th>
                <th data-options="field:'orgName',hidden:false,align:'center',width:120">属地分公司</th>
                <th data-options="field:'channelEntityName',align:'center',width:160">网点名称</th>
                <th data-options="field:'adjustFee',align:'center',width:180">调整费</th>
                <th data-options="field:'qualityChase',align:'center',width:160">质量追溯</th>
                <th data-options="field:'outDeduction',align:'center',width:180">违规抵扣</th>
                <th data-options="field:'fileName',align:'center',width:120">附件名称</th>
                <th data-options="field:'filePath',hidden:true,align:'center',width:120">附件路径</th>
            </tr>
            </thead>
        </table>
    </div>
    <!-- 数据显示结束 -->
</div>
<!-- 容器结束 -->
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script>
    BUI.use('module/fee/threeNodeNameApprove', function (threeNodeNameApprove) {
        new threeNodeNameApprove();
    });
</script>
</html>