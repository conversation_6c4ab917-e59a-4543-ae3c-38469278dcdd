<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
<head>
    <head>
        <title>第三方行销人员考核审批</title>
        <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
        <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
    </head>
<body>
<!-- 容器开始 -->
<div class="container">

    <!-- operate 开始 -->
    <div id="tb">
        <div>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true"
               id="approveBtn">审批通过</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true"
               id="disApproveBtn">审批驳回</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="icon-excel:'icon-save',plain:true"
               id="downLoadBtn">附件下载</a>
        </div>
    </div>
    <!-- operate 结束 -->

    <!-- 数据显示开始 -->
    <div class="row">
        <table id="queryMyGrid" title="查询结果"
               data-options="pageSize:10,height:350,rownumbers:true,singleSelect:false,fitColumns:true,striped:true,loadMsg:'加载中...'">
            <thead>
            <tr>
                <th data-options="field:'ck',checkbox:true"></th>
                <th data-options="field:'doneCode',hidden:false,align:'center',width:120">流水编号</th>
                <th data-options="field:'channelEntityId',hidden:false,align:'center',width:120">网点Id</th>
                <th data-options="field:'billMonth',align:'center',width:100">月份</th>
                <th data-options="field:'channelEntityName',align:'center',width:160">网点名称</th>

                <th data-options="field:'billMonthScore',align:'center',width:160">当月考核分</th>
                <th data-options="field:'assesGridScore',align:'center',width:160">其中：网格长考核打分</th>
                <th data-options="field:'basicCostFee',align:'center',width:160">基本费用分配</th>
                <th data-options="field:'marketAdjustFee',align:'center',width:160">当月调整费</th>
                <%--<th data-options="field:'assesmentScore',align:'center',width:180">行销人员考核得分</th>
                <th data-options="field:'assesTTScore',align:'center',width:160">行销人员铁通考核得分</th>
                <th data-options="field:'negativePenaltyScore',align:'center',width:180">负向扣罚得分</th>--%>
                <th data-options="field:'fileName',align:'center',width:120">附件名称</th>
                <th data-options="field:'filePath',hidden:true,align:'center',width:120">附件路径</th>
            </tr>
            </thead>
        </table>
    </div>
    <!-- 数据显示结束 -->
    <fieldset class="span24" style="margin-top: 50px" id="areaList">
        <legend>审批流程</legend>
        <div class="row">
            <div class="control-group span8">
                <label class="control-label">审批流程：</label>
                <div class="controls">
                    <select type="text" class="easyui-validatebox" style="width: 150px"
                            data-options="panelHeight:'200',editable:true,valueField:'id',textField:'text'"
                            name="flowAjudt" id="flowAjudt">
                        <Option value="1">属地内部流程</Option>
                    </select>
                </div>
            </div>
            <div class="control-group span8">
                <label class="control-label">属地二级经理：</label>
                <div class="controls">
                    <select type="text" class="easyui-validatebox" style="width: 150px"
                            data-options="panelHeight:'200',editable:true,valueField:'id',textField:'text'"
                            name="flowThreeAmader" id="flowThreeAmader">
                        <Option value="3">属地分管二级经理</Option>
                    </select>
                </div>
            </div>
            <div class="control-group span8">
                <label class="control-label">属地分管二级审批人：</label>
                <div class="controls">
                    <select type="text" class="easyui-validatebox" style="width: 150px"
                            data-options="panelHeight:'200',editable:true,valueField:'id',textField:'text'"
                            name="flowThreeAmaderName" id="flowThreeAmaderName">
                    </select>
                </div>
            </div>
        </div>
    </fieldset>
</div>
<!-- 容器结束 -->
</body>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/common/jquery.datagrid.extend.js"></script>
<script>
    BUI.use('module/fee/busiThirdXXAssessApprove', function (busiThirdXXAssessApprove) {
        new busiThirdXXAssessApprove();
    });
</script>
</html>