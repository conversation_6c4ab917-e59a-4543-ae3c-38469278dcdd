/**
 * <AUTHOR>
 * Created on 2025/3/3 18:05
 * Copyright 2016 Asiainfo Technologies(China),Inc. All rights reserved.
 */

define(['common/organizationTree', 'common/util','common/accMath'], function (require) {
    var _4ABank = require('common/util');
    var accMath = require('common/accMath');

    function mobileStoreAssessManage(config) {
        mobileStoreAssessManage.superclass.constructor.call(this, config);
        this.init();
    }

    mobileStoreAssessManage.ATTRS = {};
    BUI.extend(mobileStoreAssessManage, BUI.Base);
    BUI.augment(mobileStoreAssessManage, {
        init: function () {
            // layout
            var myLayout = new dhtmlXLayoutObject({
                parent: document.body,
                pattern: "1C",
                cells: [{
                    id: "a",
                    header: false
                }]
            });
            // Form
            var formStructure = [{
                type: "settings",
                position: "label-left",
                labelAlign: "right"
            }, {
                type: "fieldset",
                label: " 手机卖场考核查询",
                offsetLeft: 20,
                offsetTop: 20,
                width: _width - 60,
                list: [{
                    type: "block", offsetTop: 2, offsetLeft: 40,
                    list: [{
                        type: "combo",
                        name: "districtId",
                        label: "属地分公司:",
                        inputWidth: 120,
                        labelWidth: 120,
                        readonly: true,
                        labelAlign: "right"
                    },{
                        type: "newcolumn",
                        offset: 1
                    },
                        {
                            type: "input",
                            name: "nodeName",
                            label: "网点名称:",
                            inputWidth: 120,
                            labelWidth: 120,
                            labelAlign: "right"
                        }, {
                            type: "newcolumn",
                            offset: 1
                        }, {
                            type: "calendar",
                            name: 'billMonth',
                            dateFormat: "%Y%m",
                            label: '考核月份:',
                            width: 120,
                            labelWidth: 160,
                            labelAlign: "right"
                        }]
                }, {
                    type: "block",
                    offsetTop: 2,
                    offsetLeft: 68,
                    list: [{type: "button", name: "queryBtn", width: 30, offsetTop: 2, offsetLeft: 72, value: "查询"},
                        {type: "newcolumn", offset: 1},
                        {type: "button", name: "resetBtn", width: 30, offsetTop: 2, value: "重置"}]
                }]
            }, {
                type: "fieldset",
                label: "手机卖场考核信息",
                offsetLeft: 20,
                offsetTop: 20,
                width: _width - 60,
                list: [{
                    type: "block",
                    name: "toolbarRow"
                }, {
                    type: "block",
                    name: "myGridRow"
                }, {
                    type: "block",
                    name: "pagingAreaRow"
                }]
            }];
            var tableWidth = document.documentElement.clientWidth * 4 / 5;

            var myForm = myLayout.cells("a").attachForm(formStructure);
            this.set("myForm", myForm);
            this.set("myLayout", myLayout);
            myForm.enableLiveValidation(true);


            myForm.addItem("toolbarRow", {
                type: "button",
                name: "editBtn",
                width: 30,
                offsetTop: 2,
                offsetLeft: 11,
                value: "修改"
            });
            myForm.addItem("toolbarRow", {type: "newcolumn", offset: 10});
            myForm.addItem("toolbarRow", {
                type: "button",
                name: "delBtn",
                width: 30,
                offsetTop: 2,
                offsetLeft: 11,
                value: "删除"
            });
            myForm.addItem("toolbarRow", {type: "newcolumn", offset: 10});
            myForm.addItem("toolbarRow", {
                type: "button",
                name: "exportBtn",
                width: 30,
                offsetTop: 2,
                offsetLeft: 11,
                value: "导出"
            });

            myForm.addItem("myGridRow", {
                type: "container",
                offsetLeft: 10,
                offsetTop: 10,
                name: "mygrid",
                inputHeight: _height - 260,
                inputWidth: tableWidth
            });
            myForm.addItem("pagingAreaRow", {
                type: "container",
                name: "pagingArea",
                label: "",
                inputWidth: tableWidth,
                inputHeight: 30
            });

            var mygrid = new dhtmlXGridObject(myForm.getContainer("mygrid"));
            this.set("mygrid", mygrid);
            mygrid.setHeader("nodeId,agentId,districtId,属地,代理商,网点名称,考核月份,考核得分,附件名称,附件路径");
            mygrid.setColumnIds("nodeId,agentId,districtId,districtName,agentName,nodeName,billMonth,assessScore,fileName,filePath");
            mygrid.setInitWidths("100,100,100,150,200,200,120,80,100,100");
            mygrid.setExportable(mygrid.getColumnId(0), false);
            mygrid.setExportable(mygrid.getColumnId(1), false);
            mygrid.setExportable(mygrid.getColumnId(2), false);
            mygrid.setExportable(mygrid.getColumnId(9), false);
            mygrid.enableAutoHeight(false);
            mygrid.setColTypes("ro,ro,ro,ro,ro,ro,ro,ro,ro,ro");
            mygrid.setColumnHidden(0, true);
            mygrid.setColumnHidden(1, true);
            mygrid.setColumnHidden(2, true);
            mygrid.setColumnHidden(9, true);
            mygrid.setColAlign("center,center,center,center,center,center,center,center,center,center");
            mygrid.enablePaging(true, 10, null, myForm.getContainer("pagingArea"), true);
            mygrid.init();
            mygrid.setPagingSkin("bricks");

            myForm.getCombo("districtId").addOption(BUI.getComboData(10002,1));

            mygrid.attachEvent("onRowDblClicked", function(rId,cInd){
                if(cInd==8){
                    var fileName=mygrid.cellById(rId, 8).getValue();
                    var filePath=mygrid.cellById(rId, 9).getValue();
                    var util = new Util();
                    util.download(filePath,fileName, true)
                    return;
                }
            });

            var _util = new Util().dhtmlxProcessOn("正在加载。。。");
            var _this = this;
            $.post(BUI.ctx + '/agentAward/getOrganizationId', function (data) {
                new Util().dhtmlxProcessOff(_util);
                if (data.orgId != 6){
                    myForm.getCombo("districtId").setComboValue(BUI.getComboboxData(10002,null,null,null,null,data.orgId).data[0].value)
                    
                    var myFormN = _this.get("myForm");
                    myFormN.disableItem("districtId");
                }
            });

            this.initForm();
            this.initDomEvent();
        },
        initDomEvent: function () {
            var _this = this;
            var myForm = _this.get("myForm");
            var myGrid = _this.get("mygrid");

            myForm.attachEvent("onButtonClick", function (name) {
                if (name == 'queryBtn') {
                    _this.queryForm();
                } else if (name == 'resetBtn') {
                    location.href = location.href;
                } else if (name == 'exportBtn') {
                    var districtId = myForm.getItemValue("districtId");
                    var nodeName = myForm.getItemValue("nodeName");
                    var billMonth = myForm.getCalendar("billMonth").getFormatedDate("%Y%m");
                    var param = {};
                    param.nodeName = nodeName;
                    param.billMonth = billMonth;
                    param.districtId = districtId;
                    var url = "/channelNodeScore/mobileStoreAssessQuery";
                    var fileName = "手机卖场考核信息";
                    var formatData = "{}";
                    myGrid.exportData(url, param, fileName, formatData);
                } else if (name == 'editBtn') {
                    //获取行
                    var rowId = myGrid.getSelectedRowId();
                    if (rowId == null) {
                        dhtmlx.alert({
                            title: "信息", text: "请选择需要修改的信息！", type: "alert-warning"
                        });
                        return;
                    }
                    //下面是将值进行循环
                    var selectedData = {};  //定义集合
                    var columns = myGrid.columnIds;
                    for (var i = 0; i < columns.length; i++) {
                        selectedData[columns[i]] = myGrid.cells(rowId, i).getValue();
                    }
                    var billMonthY=new Date().getFullYear();
                    var billMonthM=new Date().getMonth()+1;
                    if(billMonthM<10){
                        billMonthM="0"+billMonthM
                    }
                    if (billMonthM == 0){
                        billMonthY = billMonthY - 1;
                        billMonthM = 12;
                    }
                    var nowMonth =billMonthY+""+billMonthM;
                    if (selectedData.billMonth != nowMonth-1) {
                        dhtmlx.alert({
                            title: "信息", text: "只能修改考核月当月录入的数据！", type: "alert-warning"
                        });
                        return;
                    }

                    var mywins = new dhtmlXWindows();
                    var mywin = mywins.createWindow({
                        id: 'window_id',
                        width: document.documentElement.clientWidth * 4 / 7,
                        height: document.documentElement.clientHeight * 4 / 10,
                        center: true,
                        park: false,
                        resize: false,
                        modal: true
                    });
                    mywin.setText("手机卖场考核修改");
                    mywin.keepInViewport(true);

                    var queryFormData = [{
                        type: "fieldset",
                        offsetLeft: 30,
                        label: "手机卖场考核修改",
                        width: document.documentElement.clientWidth * 4 / 7,
                        list: [{
                            type: "block",
                            offsetTop: 10,
                            list: [{
                                type: "hidden",
                                name: "nodeId"
                            }, {
                                type: "hidden",
                                name: "agentId"
                            }, {
                                type: "hidden",
                                name: "districtId"
                            }, {
                                type: "input",
                                name: "districtName",
                                label: "属地:",
                                inputWidth: 120,
                                readonly: true,
                                disabled: true,
                                labelWidth: 160,
                                labelAlign: "right"
                            }, {
                                type: "newcolumn",
                                offset: 40
                            }, {
                                type: "input",
                                name: "agentName",
                                label: "代理商名称:",
                                inputWidth: 120,
                                readonly: true,
                                disabled: true,
                                labelWidth: 160,
                                labelAlign: "right"
                            }]
                        }, {
                            type: "block",
                            offsetTop: 10,
                            list: [{
                                type: "input",
                                name: "nodeName",
                                label: "网点名称:",
                                inputWidth: 120,
                                readonly: true,
                                disabled: true,
                                labelWidth: 160,
                                labelAlign: "right"
                            },{
                                type: "newcolumn",
                                offset: 40
                            }, {
                                type: "input",
                                name: "billMonth",
                                label: "月份:",
                                inputWidth: 120,
                                readonly: true,
                                disabled: true,
                                labelWidth: 160,
                                labelAlign: "right"
                            }]
                        },{
                            type: "block",
                            offsetTop: 10,
                            list: [{
                                type: "input",
                                name: "assessScore",
                                label: "考核得分:",
                                inputWidth: 120,
                                labelWidth: 160,
                                labelAlign: "right"
                            }]
                        }]
                    }, {
                        type: "block",
                        blockOffset: 220,
                        list: [{type: "button", name: "submitsWin", width: 30, value: "确定"},
                            {type: "newcolumn", offset: 70},
                            {type: "button", name: "cancelWin", width: 30, value: "取消"}]
                    }];

                    var winform = mywin.attachForm(queryFormData);
                    winform.load({data: selectedData});

                    winform.attachEvent("onChange", function (name, value) {
                        var reg = /^(0|[1-9][0-9]?|100)$/;
                        if (name ="assessScore"){
                            if (!reg.test(value)) {
                                dhtmlx.alert({
                                    title:"提示",text:"请输入[0-100]有效的数字！！",type:"alert-warning"
                                });
                                return false;
                            }
                        }
                    });

                    winform.attachEvent("onButtonClick", function (name) {
                        if (name == "cancelWin") {
                            mywins.unload();
                        } else if (name =="submitsWin") {
                            var reg = /^(0|[1-9][0-9]?|100)$/;
                            var nodeId = winform.getItemValue("nodeId");
                            var billMonth = winform.getItemValue("billMonth");
                            var assessScore = winform.getItemValue("assessScore");
                            if (!reg.test(assessScore)) {
                                dhtmlx.alert({
                                    title:"提示",text:"考核得分请输入[0-100]有效的数字！！",type:"alert-warning"
                                });
                                return;
                            }

                            dhtmlx.message({
                                type: "confirm-warning",
                                text: "您确定要修改手机卖场考核得分吗？",
                                ok: "确定",
                                cancel: "取消",
                                callback: function (a) {
                                    if (a) {
                                        var _util = new Util().dhtmlxProcessOn("正在操作。。。");
                                        var param = {
                                            nodeId: nodeId,
                                            billMonth: billMonth,
                                            assessScore: assessScore
                                        };
                                        $.post(BUI.ctx + '/channelNodeScore/mobileStoreAssessEdit', param, function (data) {
                                            new Util().dhtmlxProcessOff(_util);
                                            if ("1" == data.status) {
                                                dhtmlx.alert({title: "错误", text: data.message, type: "alert-error"});
                                            } else if ("0" == data.status) {
                                                dhtmlx.alert({
                                                    title: '消息', text: data.message, callback: function () {
                                                        mywins.unload();
                                                        _this.queryForm();
                                                    }
                                                });
                                            }
                                        });

                                    }
                                }
                            });
                        }
                    });
                } else if (name == "delBtn") {
                    //获取行
                    var rowId = myGrid.getSelectedRowId();
                    if (rowId == null) {
                        dhtmlx.alert({
                            title: "信息", text: "请选择需要删除的信息！", type: "alert-warning"
                        });
                        return;
                    }
                    var param = {};
                    var columns = myGrid.columnIds;
                    for (var i = 0; i < columns.length; i++) {
                        if (columns[i] == "nodeId") {
                            param[columns[i]] = myGrid.cells(rowId, i).getValue();
                        }
                        if (columns[i] == "billMonth") {
                            param[columns[i]] = myGrid.cells(rowId, i).getValue();
                        }
                    }
                    var billMonthY=new Date().getFullYear();
                    var billMonthM=new Date().getMonth()+1;
                    if(billMonthM<10){
                        billMonthM="0"+billMonthM
                    }
                    if (billMonthM == 0){
                        billMonthY = billMonthY - 1;
                        billMonthM = 12;
                    }
                    var nowMonth =billMonthY+""+billMonthM;

                    if (param.billMonth != nowMonth-1) {
                        dhtmlx.alert({
                            title: "信息", text: "只能删除考核月当月录入的数据！", type: "alert-warning"
                        });
                        return;
                    }

                    dhtmlx.confirm({
                        title: "提示", text: "您确定要删除手机卖场考核吗？", type: "alert-warning", ok: "确认", cancel: "取消",
                        callback: function (r) {
                            if (r) {
                                var url = BUI.ctx + '/channelNodeScore/mobileStoreAssessDel';
                                $.post(url, param, function (result) {
                                    if (result.status == '1') {
                                        dhtmlx.alert({
                                            title: "警告！", text: result.message, type: "alert-error"
                                        });
                                    } else if (result.status == "0") {
                                        dhtmlx.alert({
                                            title: '消息', text: result.message, callback: function () {
                                                _this.queryForm();
                                            }
                                        });
                                    }
                                }, "json");
                            }
                        }
                    });
                }
            });
        },
        initForm: function () {
            var _this = this;

        }, queryForm: function () {
            var _this = this;

            var cells_a = _this.get("myLayout").cells("a");
            var myForm = _this.get("myForm");
            var myGrid = _this.get("mygrid");

            var districtId = myForm.getItemValue("districtId");
            var nodeName = myForm.getItemValue("nodeName");
            var billMonth = myForm.getCalendar("billMonth").getFormatedDate("%Y%m");
            var param = {};
            param.nodeName = nodeName;
            param.billMonth = billMonth;
            param.districtId = districtId;

            cells_a.progressOn();
            var url = BUI.ctx + "/channelNodeScore/mobileStoreAssessQuery?" + encodeURI(encodeURI($.param(param)));
            myGrid.clearAndLoad(url, function () {
                cells_a.progressOff();
            }, "js");
        }
    });
    return mobileStoreAssessManage;
});


var checkBusiHealthRatio =  function(data, name) {
    data = data + "";
    var d = /^[0,1]{1}$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};
var checkEnterPersonnelAssessRatio = function(data, name) {
    if (data < 0 || data > 1) {
        return false;
    }
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};
var checkHallBasicManageRatio = function(data, name) {
    if (data < 0 || data > 1) {
        return false;
    }
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};

var checkSpecialWorkAppraiseRatio = function(data, name) {
    if (data < 0 || data > 1) {
        return false;
    }
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};

var checkEnterPersonnelNum = function(data, name) {
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};
var checkSatisfyDegreeRatio = function(data, name) {
    if (data < 0 || data > 1) {
        return false;
    }
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};
var checkHallUnifyAssessRatio = function(data, name) {
    if (data < 0 || data > 1) {
        return false;
    }
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};
var checkStimulateProjectAssessRatio = function(data, name) {
    if (data < 0 || data > 1) {
        return false;
    }
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};
var checkOperateSupportAssessRatio = function(data, name) {
    if (data < 0 || data > 1) {
        return false;
    }
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};

var checkBasicSupportAssessRatio = function(data, name) {
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};
var checkBasicProjectAssessRatio = function(data, name) {
    if (data < 0 || data > 1) {
        return false;
    }
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    if(data ==0.00 ){
        return true;
    }
    return true;
};
var checkBasicSupportAssessRatio = function(data, name) {
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};
var checkTerritoryAssessAmount = function(data, name) {
    data = data + "";
    var d = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (!d.test(data)) {
        return false;
    }
    return true;
};

