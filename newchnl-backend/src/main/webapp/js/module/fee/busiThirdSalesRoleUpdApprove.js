
define(['common/organizationTree','common/util'], function(require) {
    var Util = require('common/util');
    var crytoJs = require('common/crytoJs');
	function busiThirdSalesRoleUpdApprove(config){
        busiThirdSalesRoleUpdApprove.superclass.constructor.call(this, config);
		this.init();
	}
    busiThirdSalesRoleUpdApprove.ATTRS = {
	};
	BUI.extend(busiThirdSalesRoleUpdApprove, BUI.Base);
	BUI.augment(busiThirdSalesRoleUpdApprove, {
		init : function(){
			this.initDomEvent();
		},

		initDomEvent:function(){
			var _this = this;
			var queryMyGrid = _this.get("queryMyGrid");
            //获取系统当前时间 查询的是录入月数据
            var billMonthY=new Date().getFullYear();
            var billMonthM=new Date().getMonth();
            if(billMonthM<10){
                billMonthM="0"+billMonthM
            }
            if (billMonthM == 0){
                billMonthY = billMonthY - 1;
                billMonthM = 12;
            }
            var nowMonth =billMonthY+""+billMonthM;
            var flowThreeAmader = $("#flowThreeAmader").val();
            var url = BUI.ctx + "/agentAdjustFeeCller/queryResTypeByOrgIdSpe?resType=" + flowThreeAmader;
            var data = $.ajax({
                url: url,
                async: false,
                dataType: "json"
            }).responseText;
            data = eval("(" + data + ")").options;
            if (data != null){
                for(var i=0;i<data.length;i++){
                    $("#flowThreeAmaderName").append($('<option></option>').attr('value', data[i].value).text(data[i].text));
                }
            }
            $.messager.alert("提示", "待属地市场部三级审批工单需选择审批人员信息，请须知！", "warning");


            //所有button事件
            //查询
            $("#queryMyGrid").datagrid({
                toolbar:"#tb",
                pagination: true,
                pageSize: 10,
                fitColumns: false,
                url: BUI.ctx + "/agentAward/busiThirdSalesRoleUpdInfoQuery",
                onBeforeLoad: function (param) {
                    param.recStatus = "1";
                    param.billMonth = nowMonth;
                }
            });
            $("#queryMyGrid").datagrid("load");

            //审批通过 按钮
            $("#approveBtn").click(function () {
                var auditStatus = null;
                var rows = $("#queryMyGrid").datagrid('getSelections');
                if (rows.length == 0) {
                    $.messager.alert("提示", "未选择需要审批的信息！", "warning");
                    return;
                }
                var  flowThreeAmaderName= $("#flowThreeAmaderName").val();
                if (rows[0].auditStatus == 0){
                    if (flowThreeAmaderName == null || flowThreeAmaderName == ""){
                        $.messager.alert("提示", "待属地市场部三级审批工代需选择审批人员信息，请选择审批人！", "warning");
                        return;
                    }
                    auditStatus=1; // 属地市场部三级审批通过
                }else {
                    auditStatus=3; //市场部职能审批通过
                }
                var param = {};
                var doneCode = rows[0].doneCode;
                param.doneCodes = doneCode;
                param.auditStatus = auditStatus;
                param.agentAdjustUseId = flowThreeAmaderName;
                param.recStatus = "1";
                if (doneCode != null) {
                    util = new Util().dhtmlxProcessOn("正在审批中...");
                    $.ajax({
                        type: "POST",
                        contentType: 'application/json',
                        dataType: 'JSON',
                        url: BUI.ctx + '/agentAdjustFeeCller/operateBusiThirdSalesRoleUpdApprove',
                        data: JSON.stringify(param),
                        success: function (result) {
                            new Util().dhtmlxProcessOff(util);
                            if ("EXCEPTION" == result.type) {
                                $.messager.alert('错误', result.msg, 'error');
                            } else if ("FINISH" == result.type) {
                                $.messager.alert("提示",result.msg,"info");
                            }
                            $("#queryMyGrid").datagrid("load");
                        }
                    });
                }
            });
            //审批驳回
            $("#disApproveBtn").click(function () {
                var rows = $("#queryMyGrid").datagrid('getSelections');
                var auditStatus = null;
                var  flowThreeAmaderName= $("#flowThreeAmaderName").val();
                if (rows.length == 0) {
                    $.messager.alert("提示", "未选择需要审批的信息！", "warning");
                    return;
                }
                if (rows[0].auditStatus == 0){
                    auditStatus=2; // 属地市场部三级审批通过
                }else {
                    auditStatus=4; //市场部职能审批通过
                }
                var param = {};
                var doneCode = rows[0].doneCode;
                param.doneCodes = doneCode;
                param.auditStatus = auditStatus;
                param.recStatus = "2";
                param.agentAdjustUseId = flowThreeAmaderName;
                util = new Util().dhtmlxProcessOn("正在审批中...");
                if (doneCode != null) {
                    $.ajax({
                        type: "POST",
                        contentType: 'application/json',
                        dataType: 'JSON',
                        url: BUI.ctx + '/agentAdjustFeeCller/operateBusiThirdSalesRoleUpdApprove',
                        data: JSON.stringify(param),
                        success: function (result) {
                            new Util().dhtmlxProcessOff(util);
                            if ("EXCEPTION" == result.type) {
                                $.messager.alert('错误', result.msg, 'error');
                            } else if ("FINISH" == result.type) {
                                $.messager.alert("提示",result.msg,"info");
                            }
                            $("#queryMyGrid").datagrid("load");
                        }
                    });
                }
            });

            //附件下载
            $("#downLoadBtn").click(function () {
                var fileName = "";
                var filePath = "";
                var rows = $("#queryMyGrid").datagrid('getSelections');
                if (rows.length == 0) {
                    $.messager.alert("提示", "请选择一条信息进行附件下载！", "warning");
                    return;
                }
                if(rows.length > 1) {
                    $.messager.alert("提示", "一次只能导出一条数据的附件！", "warning");
                    return;
                }

                fileName = rows[0].fileName;
                filePath = rows[0].filePath;
                if (fileName != '' && fileName != null){
                    var util = new Util();
                    util.download(filePath,fileName, true);
                    return;
                } else{
                    $.messager.alert("提示", "一次只能导出一条数据的附件！", "error");
                    return;
                }
            });
		}
	});
	return busiThirdSalesRoleUpdApprove;
});