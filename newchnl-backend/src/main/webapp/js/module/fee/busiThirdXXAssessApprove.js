
define(['common/organizationTree','common/util'], function(require) {
    var Util = require('common/util');
    var crytoJs = require('common/crytoJs');
	function busiThirdXXAssessApprove(config){
        busiThirdXXAssessApprove.superclass.constructor.call(this, config);
		this.init();
	}
    busiThirdXXAssessApprove.ATTRS = {
	};
	BUI.extend(busiThirdXXAssessApprove, BUI.Base);
	BUI.augment(busiThirdXXAssessApprove, {
		init : function(){
			this.initDomEvent();
		},

		initDomEvent:function() {

            // 获取系统当前时间 查询的是T-1月录入的数据
            const now = new Date()
            var billMonthY = now.getFullYear()
            var billMonthM = now.getMonth()
            if (billMonthM < 10) {
                billMonthM = '0' + billMonthM
            }
            if (billMonthM == 0) {
                billMonthY = billMonthY - 1
                billMonthM = 12
            }
            var nowMonth = billMonthY + '' + billMonthM
            //所有button事件
            //查询
            /* myGrid.clearAndLoad(BUI.ctx + '/agentAward/busiThirdXXAssessInfoQuery?' + $.param({
                // billMonth: myForm.getCalendar('queryBillMonth').getFormatedDate('%Y%m'),
                recStatusList: '0,1',
                // 后台会按主机时间是上个月过滤，操作员只能查询自己审批的数据
            }), function () {
            }, 'js') */
            // 设置二级审批人的第三个框的枚举值
            var flowThreeAmader = $('#flowThreeAmader').val()
            var data = $.ajax({
                url: BUI.ctx + '/agentAdjustFeeCller/queryResTypeByOrgId?resType=' + flowThreeAmader,
                async: false,
                dataType: 'json',
            }).responseText
            data = eval('(' + data + ')').options
            if (data != null) {
                for (var i = 0; i < data.length; i++) {
                    $('#flowThreeAmaderName').append($('<option></option>').attr('value', data[i].value).text(data[i].text))
                }
            }
            $.messager.alert("提示", "待属地内部流程审批工单需选择二级审批人员信息，请须知！", "warning");



            $('#queryMyGrid').datagrid({
                toolbar: '#tb',
                pagination: true,
                pageSize: 10,
                fitColumns: false,
                url: BUI.ctx + '/agentAward/busiThirdXXAssessInfoQuery',
                onBeforeLoad: function (param) {
                    param.recStatusList = '0,1'
                    param.billMonth = nowMonth
                },
            })
            // $('#queryMyGrid').datagrid('load')

            // 审批通过按钮
            $('#approveBtn').click(function () {
                var rows = $('#queryMyGrid').datagrid('getSelections')
                if (rows.length == 0) {
                    $.messager.alert('提示', '未选择需要审批的信息！', 'warning')
                    return
                }
                var param = {}
                // 三级审批校验
                var flowThreeAmaderName = $('#flowThreeAmaderName').val();
                if (rows[0].recStatus == 0) { // 0 代表一级审批还没过，需要指定二级审批人
                    if (flowThreeAmaderName == null || flowThreeAmaderName == '') {
                        $.messager.alert('提示', '待属地内部流程审批工单需选择二级审批人员信息，请选择审批人！', 'warning')
                        return
                    } else {
                        param.agentAdjustUseId = flowThreeAmaderName;
                        param.newRecStatus = '1'
                    }
                } else if (rows[0].recStatus == '1') { // recStatus=1的情况
                   param.newRecStatus = '3' //市场部职能审批通过
                } else {
                    $.messager.alert('提示', '该数据审批状态异常，请联系管理员！', 'warning')
                    return
                }
                var doneCode = rows[0].doneCode
                var nodeName = rows[0].channelEntityName
                param.doneCodes = doneCode
                param.channelEntityName = nodeName


                if (doneCode != null) {
                    $.ajax({
                        type: 'POST',
                        contentType: 'application/json',
                        dataType: 'JSON',
                        url: BUI.ctx + '/agentAdjustFeeCller/operateBusiThirdXXAssessApprove',
                        data: JSON.stringify(param),
                        success: function (result) {
                            if ('EXCEPTION' == result.type) {
                                $.messager.alert('错误', result.msg, 'error')
                            } else if ('FINISH' == result.type) {
                                $.messager.alert('提示', result.msg, 'info')
                            }
                            $('#queryMyGrid').datagrid('load')
                        },
                    })
                }
            })
            /*myForm.attachEvent("onChange", function (name, value) {
                if(name == "flowAmaderCharge"){
                    var flowAmaderCharge = myForm.getCombo("flowAmaderCharge").getSelectedValue();
                    myForm.getCombo("flowAmaderChargeName").load(BUI.ctx + "/agentAdjustFeeCller/queryResTypeByOrgId?resType=" + flowAmaderCharge);
                }
            });*/


            // 审批驳回按钮
            $('#disApproveBtn').click(function () {
                var rows = $('#queryMyGrid').datagrid('getSelections')
                if (rows.length == 0) {
                    $.messager.alert('提示', '未选择需要审批的信息！', 'warning')
                    return
                }
                var param = {}
                var doneCode = rows[0].doneCode
                var nodeName = rows[0].channelEntityName
                param.doneCodes = doneCode
                param.channelEntityName = nodeName

                if (rows[0].recStatus == '0') {
                    param.newRecStatus = '2'
                } else if (rows[0].recStatus == '1') {
                    param.newRecStatus = '4'
                } else {
                    $.messager.alert('提示', '该数据审批状态异常，请联系管理员！', 'warning')
                    return
                }
                if (doneCode != null) {
                    $.ajax({
                        type: 'POST',
                        contentType: 'application/json',
                        dataType: 'JSON',
                        url: BUI.ctx + '/agentAdjustFeeCller/operateBusiThirdXXAssessApprove',
                        data: JSON.stringify(param),
                        success: function (result) {
                            if ('EXCEPTION' == result.type) {
                                $.messager.alert('错误', result.msg, 'error')
                            } else if ('FINISH' == result.type) {
                                $.messager.alert('提示', result.msg, 'info')
                            }
                            $('#queryMyGrid').datagrid('load')
                        },
                    })
                }
            })

            //附件下载
            $('#downLoadBtn').click(function () {
                var fileName = ''
                var filePath = ''
                var rows = $('#queryMyGrid').datagrid('getSelections')
                if (rows.length == 0) {
                    $.messager.alert('提示', '请选择一条信息进行附件下载！', 'warning')
                    return
                }
                if (rows.length > 1) {
                    $.messager.alert('提示', '一次只能导出一条数据的附件！', 'warning')
                    return
                }

                fileName = rows[0].fileName
                filePath = rows[0].filePath
                if (fileName != '' && fileName != null) {
                    var util = new Util()
                    util.download(filePath, fileName, true)
                    return
                } else {
                    $.messager.alert('提示', '该条数据没有上传附件，请您重新确认！', 'error')
                    return
                }
            })
        }
	});
	return busiThirdXXAssessApprove;
});