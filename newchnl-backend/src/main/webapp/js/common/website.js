define(function(require) {
	Website = function(){
		Website.superclass.constructor.call(this);
		this.init();
	};
	Website.ATTRS = {
	};
	BUI.extend(Website, BUI.Base);
	BUI.augment(Website, {
		//初始化
		init : function(){
			var mywins = new dhtmlXWindows();
			this.set("mywins",mywins);

		},
		openWindow: function(accId,nodeKind){
			var mywins = this.get("mywins");
			var mywin = mywins.createWindow({
				id:'websiteWindow',
			    width:430,
			    height:400,
			    center:true,
			    park:false,
			    resize:false,
			    modal:true
			});
			mywin.button('park').hide();
			mywin.button('minmax').hide();
			mywin.setText("网点查询");
			mywin.keepInViewport(true);
			
			var queryFormData = [
				{type:"block", offsetTop:10,list:[
					{type: "input", name:"websiteName_q", label: "网点名称：", inputWidth:150},
					{type: "newcolumn", offset:50},
					{type: "button", name:"queryCircleBtn", value: "搜索",offsetTop:0}
				]},
				{type:"container", name:"mylayout", label:"", inputWidth:400, inputHeight:260},
				{type:"container", name:"pagingArea", label:"", inputWidth:400,offsetLeft:10}
			];
			var myform = mywin.attachForm(queryFormData);
			
			var mylayout = new dhtmlXLayoutObject({
				parent: myform.getContainer('mylayout'),
				pattern: "1C",
				offsets: {
					top:0,right:10,bottom:10,left:10
				},
				cells: [{
					id:'a',text:'网点信息'
				}]
			});
			
			var mytoolbar = mylayout.cells("a").attachToolbar();
			mytoolbar.setIconsPath(BUI.ctx+'/assets/easyui/themes/icons/');
			mytoolbar.addButton("addCircleBtn", 0, "确认选择",'ok.png');
			
			var _this = this;
			mytoolbar.attachEvent("onClick", function(id){
				var selectedId = mygrid.getSelectedRowId();
				if (selectedId == null){
		    		dhtmlx.alert({
		    			title:"警告",text:"请选择一个网点！",type:"alert-warning", ok:'确定'
		    		});
		    		return;
				}
				var selectedData = {};
				var columns = mygrid.columnIds;
				for (var i = 0; i < columns.length; i++){
					selectedData[columns[i]] = mygrid.cells(selectedId,i).getValue();
				}
				_this.fire('onSelect',selectedData);
				mywins.unload();
			});
			
			var mygrid = mylayout.cells("a").attachGrid();
			mygrid.setEditable(false);
			mygrid.setHeader("网点编号,网点名称");
			mygrid.setColumnIds("nodeId,busiName");
			//mygrid.setColumnHidden(0, true);
			//mygrid.setColumnHidden(1, true);
			mygrid.setInitWidths("100,400");
			mygrid.setColAlign("center,left");
			//mygrid.enableAutoHeight(false);
			mygrid.enablePaging(true,10,null,myform.getContainer('pagingArea'),true);
			mygrid.init();
			mygrid.setPagingSkin("bricks");
			mygrid.attachEvent("onXLE",function(gridObj){
				gridObj.forEachRow(function(rowid){
					gridObj.forEachCell(rowid,function(cellObj,ind){
						if (ind == 2){
							//cellObj.setLabel(BUI.columnFormatter('50002', cellObj.getValue()));
							gridObj.cells(rowid,ind+1).setLabel(BUI.columnFormatter('50002', cellObj.getValue()));
						}
					});
				});
			});
			var Name="";
			Name = myform.getItemValue('websiteName_q');
			Name=encodeURI(encodeURI(Name));
			_this.searchData(myform, mygrid,accId,nodeKind,Name);
			myform.attachEvent("onButtonClick", function(name){
				Name = myform.getItemValue('websiteName_q');
				Name=encodeURI(encodeURI(Name));
				_this.searchData(this, mygrid,accId,nodeKind,Name);
			});
			
		},
		searchData: function(myform, mygrid,accId,nodeKind,Name){
			var param = {};
			param.name = Name;
			param.accId=accId;
			param.nodeKind=nodeKind;
			mygrid.clearAndLoad(BUI.ctx+"/agentFundManager/getAgentBusis?"+$.param(param),function(){
		    },"js");
		}
	});
    return Website;
});